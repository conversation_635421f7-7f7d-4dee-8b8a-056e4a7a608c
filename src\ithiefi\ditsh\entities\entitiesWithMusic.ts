import { Entity, Player, system } from "@minecraft/server";

/**
 * @fileoverview Entity Music System for DitSH Add-On
 *
 * This module manages background music playback for specific entities in the game.
 * It handles music playback, stopping, and persistence across world reloads.
 *
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * Map of entity type IDs to their associated music track identifiers.
 *
 * @description Defines which entities should play music and what music they should play.
 * The key is the entity type ID, and the value is the sound identifier for the music track.
 *
 * @example
 * ```typescript
 * // Check if an entity has associated music
 * if (entitiesWithMusic.has(entity.typeId)) {
 *   const musicTrack = entitiesWithMusic.get(entity.typeId);
 *   playMusicForEntity(entity, musicTrack);
 * }
 * ```
 */
export const entitiesWithMusic = new Map<string, string>([
  ["ditsh:ao_oni", "mob.ditsh.ao_oni.chase"],
  ["ditsh:armless", "mob.ditsh.armless.chase"],
  ["ditsh:headless_horseman", "mob.ditsh.headless_horseman.chase"],
  ["ditsh:herobrine", "mob.ditsh.herobrine.chase"],
  ["ditsh:jack_black", "mob.ditsh.jack_black.chase"],
  ["ditsh:jeff", "mob.ditsh.jeff.chase"],
  ["ditsh:mac_tonight", "mob.ditsh.mac_tonight.chase"],
  ["ditsh:mama_tattletail", "mob.ditsh.mama_tattletail.chase"],
  ["ditsh:murder_monkey", "mob.ditsh.murder_monkey.chase"],
  ["ditsh:mx", "mob.ditsh.mx.chase"],
  ["ditsh:nun", "mob.ditsh.nun.chase"],
  ["ditsh:rosemary", "mob.ditsh.rosemary.chase"],
  ["ditsh:scp096", "mob.ditsh.scp096.chase"],
  ["ditsh:scp173", "mob.ditsh.scp173.chase"],
  ["ditsh:scp049", "mob.ditsh.scp049.chase"],
  ["ditsh:grunt", "mob.ditsh.grunt.chase"],
  ["ditsh:sonic", "mob.ditsh.sonic.chase"],
  ["ditsh:specimen2", "mob.ditsh.specimen2.chase"],
  ["ditsh:specimen5", "mob.ditsh.specimen5.chase"],
  ["ditsh:specimen6", "mob.ditsh.specimen6.chase"],
  ["ditsh:specimen9", "mob.ditsh.specimen9.chase"],
  ["ditsh:spider_blocksian", "mob.ditsh.spider_blocksian.chase"]
]);

/**
 * Map of entity type IDs to their music duration in ticks.
 *
 * @description Defines the duration of each entity's music track in ticks (20 ticks = 1 second).
 * This is used to determine when music should expire and allow replaying.
 * The durations are converted from the timer values in the entity behavior pack files.
 *
 * @example
 * ```typescript
 * const durationTicks = musicDurations.get("ditsh:ao_oni"); // Returns 456.6 ticks (22.83 seconds)
 * ```
 */
export const musicDurations = new Map<string, number>([
  ["ditsh:ao_oni", 22.83 * 20], // 456.6 ticks (22.83 seconds from entity file)
  ["ditsh:armless", 22 * 20], // 440 ticks (22 seconds from entity file)
  ["ditsh:headless_horseman", 81 * 20], // 1620 ticks (81 seconds from entity file)
  ["ditsh:herobrine", 24 * 20], // 480 ticks (24 seconds from entity file)
  ["ditsh:jack_black", 32 * 20], // 640 ticks (32 seconds from entity file)
  ["ditsh:jeff", 46 * 20], // 920 ticks (46 seconds from entity file)
  ["ditsh:mac_tonight", 30 * 20], // 600 ticks (30 seconds from entity file)
  ["ditsh:mama_tattletail", 39 * 20], // 780 ticks (39 seconds from entity file)
  ["ditsh:murder_monkey", 106 * 20], // 2120 ticks (106 seconds from entity file)
  ["ditsh:mx", 10 * 20], // 200 ticks (10 seconds from entity file)
  ["ditsh:nun", 22 * 20], // 440 ticks (22 seconds from entity file)
  ["ditsh:rosemary", 89 * 20], // 1780 ticks (89 seconds from entity file)
  ["ditsh:scp096", 14 * 20], // 280 ticks (14 seconds from entity file)
  ["ditsh:scp173", 5 * 20], // 100 ticks (5 seconds from entity file)
  ["ditsh:scp049", 11 * 20], // 220 ticks (11 seconds from entity file)
  ["ditsh:grunt", 10 * 20], // 200 ticks (10 seconds from entity file)
  ["ditsh:sonic", 14 * 20], // 280 ticks (14 seconds from entity file)
  ["ditsh:specimen2", 68 * 20], // 1360 ticks (68 seconds from entity file)
  ["ditsh:specimen5", 72 * 20], // 1440 ticks (72 seconds from entity file)
  ["ditsh:specimen6", 131 * 20], // 2620 ticks (131 seconds from entity file)
  ["ditsh:specimen9", 21 * 20], // 420 ticks (21 seconds from entity file)
  ["ditsh:spider_blocksian", 5 * 20] // 100 ticks (5 seconds from entity file)
]);

/**
 * Global tracking of currently playing music by entity type.
 *
 * @description Stores the entity ID and tick when music started playing
 * for each entity type. This ensures only one entity of each type can play
 * music at a time and tracks when the music should expire using Minecraft's tick system.
 *
 * Structure: Map<entityTypeId, { entityId: string, startTick: number }>
 */
const globalMusicTracker = new Map<string, { entityId: string; startTick: number }>();

/**
 * Checks if music for a specific entity type has expired based on its duration.
 *
 * @param entityTypeId - The entity type ID to check
 * @returns True if the music has expired or no music is currently tracked
 *
 * @description This function checks if the currently playing music for an entity type
 * has exceeded its expected duration and should be considered expired using Minecraft's tick system.
 */
function isMusicExpired(entityTypeId: string): boolean {
  const tracker = globalMusicTracker.get(entityTypeId);
  if (!tracker) return true;

  const durationTicks = musicDurations.get(entityTypeId);
  if (!durationTicks) return true;

  const currentTick = system.currentTick;
  const elapsedTicks = currentTick - tracker.startTick;

  return elapsedTicks >= durationTicks;
}

/**
 * Cleans up expired music entries from the global tracker.
 *
 * @description This function removes entries from the global music tracker
 * where the music duration has expired. This prevents stale entries from
 * blocking new music playback. Can be called periodically or before music operations.
 *
 * @example
 * ```typescript
 * // Clean up expired music entries periodically
 * system.runInterval(() => {
 *   cleanupExpiredMusic();
 * }, 200); // Every 10 seconds
 * ```
 */
export function cleanupExpiredMusic(): void {
  for (const [entityTypeId] of globalMusicTracker) {
    if (isMusicExpired(entityTypeId)) {
      globalMusicTracker.delete(entityTypeId);
    }
  }
}

/**
 * Checks if an entity can play music based on global restrictions.
 *
 * @param entity - The entity that wants to play music
 * @returns True if the entity can play music
 *
 * @description This function ensures only one entity of each type can play
 * music at a time. It checks if another entity of the same type is already
 * playing music and if that music hasn't expired.
 */
function canEntityPlayMusic(entity: Entity): boolean {
  const tracker = globalMusicTracker.get(entity.typeId);

  // No music currently playing for this entity type
  if (!tracker) return true;

  // Music has expired
  if (isMusicExpired(entity.typeId)) {
    globalMusicTracker.delete(entity.typeId);
    return true;
  }

  // Same entity can continue playing music
  if (tracker.entityId === entity.id) return true;

  // Different entity of same type is still playing music
  return false;
}

/**
 * Plays background music for nearby players when a specific entity is present.
 *
 * @param entity - The entity that triggers the music playback
 * @param music - The sound identifier for the music track to play
 *
 * @description This function finds all players within a 256-block radius of the entity
 * and starts playing the specified music track for them. It uses a robust system that:
 * - Ensures only one entity of each type can play music at a time
 * - Tracks music duration and automatically expires old music states
 * - Prevents duplicate music playback by checking current player music state
 * - Handles music looping properly by resetting expired music properties
 *
 * @remarks
 * - Music is played using the `playsound` command with default volume (1.0) and pitch (1.0)
 * - Player dynamic properties are used to track music playback state per entity type
 * - Global tracking ensures only one entity per type plays music simultaneously
 * - Automatic expiration prevents stale music states from blocking replays
 *
 * @example
 * ```typescript
 * const aoOniEntity = dimension.getEntities({ type: "ditsh:ao_oni" })[0];
 * if (aoOniEntity) {
 *   playMusicForEntity(aoOniEntity, "mob.ditsh.ao_oni.chase");
 * }
 * ```
 */
export function playMusicForEntity(entity: Entity, music: string): void {
  // Clean up any expired music entries first
  cleanupExpiredMusic();

  // Check if this entity can play music (global restriction)
  if (!canEntityPlayMusic(entity)) {
    return;
  }

  const nearbyPlayers: Player[] = getNearbyPlayers(entity, 256);
  let musicStarted = false;

  for (const player of nearbyPlayers) {
    let isCurrentlyPlaying: boolean = isPlayerPlayingMusic(player, entity.typeId);

    // If music is marked as playing but has expired, reset the player's state
    if (isCurrentlyPlaying && isMusicExpired(entity.typeId)) {
      player.setDynamicProperty(`${entity.typeId}_music`, false);
      isCurrentlyPlaying = false;
    }

    if (!isCurrentlyPlaying) {
      player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
      player.setDynamicProperty(`${entity.typeId}_music`, true);
      musicStarted = true;
    }
  }

  // Update global tracker if music was started
  if (musicStarted) {
    globalMusicTracker.set(entity.typeId, {
      entityId: entity.id,
      startTick: system.currentTick
    });
  }

  return;
}

/**
 * Stops background music for nearby players when a specific entity is no longer present.
 *
 * @param entity - The entity that was triggering the music playback
 * @param music - The sound identifier for the music track to stop
 *
 * @description This function finds all players within a 256-block radius of the entity
 * and stops the specified music track for them if it's currently playing. It updates
 * the player's dynamic properties to reflect that the music is no longer playing.
 *
 * @remarks
 * - Music is stopped using the `stopsound` command targeting the specific sound
 * - Player dynamic properties are updated to track that music is no longer playing
 * - Only stops music for players who are currently playing the specified track
 *
 * @example
 * ```typescript
 * // Stop music when entity dies or despawns
 * world.afterEvents.entityDie.subscribe((event) => {
 *   if (event.deadEntity.typeId === "ditsh:ao_oni") {
 *     stopMusicForEntity(event.deadEntity, "mob.ditsh.ao_oni.chase");
 *   }
 * });
 * ```
 */
export async function stopMusicForEntity(entity: Entity, music: string): Promise<void> {
  const nearbyPlayers: Player[] = getNearbyPlayers(entity, 256);

  for (const player of nearbyPlayers) {
    const isCurrentlyPlaying: boolean = isPlayerPlayingMusic(player, entity.typeId);

    if (isCurrentlyPlaying) {
      player.runCommand(`stopsound @s ${music}`);
      player.setDynamicProperty(`${entity.typeId}_music`, false);
    }
  }

  // Remove from global tracker to allow other entities of same type to play music
  const tracker = globalMusicTracker.get(entity.typeId);
  if (tracker && tracker.entityId === entity.id) {
    globalMusicTracker.delete(entity.typeId);
  }

  return;
}

/**
 * Continues playing music for an entity after world reload or entity respawn.
 *
 * @param entity - The entity that should continue playing music
 * @param music - The sound identifier for the music track to continue playing
 *
 * @description This function is called when entities are loaded back into the world
 * (such as after a world reload or chunk loading). It checks if the entity was
 * previously playing music and resumes playback for nearby players.
 *
 * @remarks
 * - Waits 140 ticks (7 seconds) before starting music to allow world stabilization
 * - Only continues music if the entity's "ditsh:playing_music" property is true
 * - Prevents duplicate music playback by checking current player music state
 * - This is an async function that uses system.waitTicks for timing control
 *
 * @example
 * ```typescript
 * // Called during entity load events
 * world.afterEvents.entityLoad.subscribe((event) => {
 *   const entity = event.entity;
 *   if (entitiesWithMusic.has(entity.typeId)) {
 *     const musicTrack = entitiesWithMusic.get(entity.typeId)!;
 *     continueMusicForEntity(entity, musicTrack);
 *   }
 * });
 * ```
 */
export async function continueMusicForEntity(entity: Entity, music: string): Promise<void> {
  const playMusic: boolean = entity.getProperty("ditsh:playing_music") as boolean;

  if (playMusic) {
    await system.waitTicks(140);

    // Clean up any expired music entries first
    cleanupExpiredMusic();

    // Check if this entity can play music (global restriction)
    if (!canEntityPlayMusic(entity)) {
      return;
    }

    const nearbyPlayers: Player[] = getNearbyPlayers(entity, 256);
    let musicStarted = false;

    for (const player of nearbyPlayers) {
      let isCurrentlyPlaying: boolean = isPlayerPlayingMusic(player, entity.typeId);

      // If music is marked as playing but has expired, reset the player's state
      if (isCurrentlyPlaying && isMusicExpired(entity.typeId)) {
        player.setDynamicProperty(`${entity.typeId}_music`, false);
        isCurrentlyPlaying = false;
      }

      if (!isCurrentlyPlaying) {
        player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
        player.setDynamicProperty(`${entity.typeId}_music`, true);
        musicStarted = true;
      }
    }

    // Update global tracker if music was started
    if (musicStarted) {
      globalMusicTracker.set(entity.typeId, {
        entityId: entity.id,
        startTick: system.currentTick
      });
    }
  }
  return;
}

/**
 * Resets all entity music for a player when they rejoin the world.
 *
 * @param player - The player whose music state should be reset
 *
 * @description This function is called when a player loads into the world
 * (such as after rejoining or respawning). It stops all entity-related music
 * that might be playing for the player and resets their music state properties.
 *
 * @remarks
 * - Iterates through all registered entities with music in the entitiesWithMusic map
 * - Stops any currently playing music tracks for the player
 * - Resets all music-related dynamic properties to false
 * - Prevents music from continuing to play inappropriately after player reconnection
 *
 * @example
 * ```typescript
 * // Called during player load events
 * world.afterEvents.entityLoad.subscribe((event) => {
 *   if (event.entity instanceof Player) {
 *     resetPlayerMusic(event.entity);
 *   }
 * });
 * ```
 */
export function resetPlayerMusic(player: Player): void {
  for (const [entityTypeId, music] of entitiesWithMusic) {
    const isCurrentlyPlaying: boolean = isPlayerPlayingMusic(player, entityTypeId);

    if (isCurrentlyPlaying) {
      player.runCommand(`stopsound @s ${music}`);
      player.setDynamicProperty(`${entityTypeId}_music`, false);
    }
  }
  return;
}

/**
 * Retrieves all players within a specified range of an entity.
 *
 * @param entity - The entity to search around
 * @param range - The maximum distance in blocks to search for players
 * @returns An array of Player objects within the specified range
 *
 * @description This helper function uses the dimension's getPlayers method
 * to find all players within a circular area around the entity's location.
 *
 * @remarks
 * - Uses the entity's current location as the center point
 * - Range is measured in blocks using Euclidean distance
 * - Returns an empty array if no players are found within range
 *
 * @example
 * ```typescript
 * const nearbyPlayers = getNearbyPlayers(aoOniEntity, 64);
 * console.log(`Found ${nearbyPlayers.length} players nearby`);
 * ```
 */
function getNearbyPlayers(entity: Entity, range: number): Player[] {
  const players: Player[] = entity.dimension.getPlayers({ location: entity.location, maxDistance: range });
  return players;
}

/**
 * Checks if a player is currently playing music for a specific entity type.
 *
 * @param player - The player to check
 * @param entityTypeId - The entity type ID to check music state for
 * @returns True if the player is currently playing music for the specified entity type
 *
 * @description This helper function checks the player's dynamic properties
 * to determine if music is currently playing for a specific entity type.
 * The property name follows the pattern: `${entityTypeId}_music`.
 *
 * @remarks
 * - Returns false if the dynamic property doesn't exist or is falsy
 * - Dynamic properties persist across game sessions
 * - Used to prevent duplicate music playback
 *
 * @example
 * ```typescript
 * if (!isPlayerPlayingMusic(player, "ditsh:ao_oni")) {
 *   // Start playing music for this entity type
 *   playMusicForEntity(entity, "mob.ditsh.ao_oni.chase");
 * }
 * ```
 */
function isPlayerPlayingMusic(player: Player, entityTypeId: string): boolean {
  const playingMusic: boolean = player.getDynamicProperty(`${entityTypeId}_music`) as boolean;
  return playingMusic ?? false;
}
