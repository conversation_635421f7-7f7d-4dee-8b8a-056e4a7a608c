{"format_version": "1.8.0", "animations": {"animation.ithiefi_ditsh_spider_blocksian.idle": {"loop": true, "animation_length": 3.5834, "bones": {"torso": {"position": [0, "math.sin(100*q.anim_time)*1", 0]}, "leftFrontLeg": {"rotation": ["math.sin(100*q.anim_time)*4", 0, 0]}, "head": {"rotation": ["-math.sin(100*q.anim_time-70)*4", 0, 0]}, "waist": {"position": [0, "math.sin(100*q.anim_time)*1", 0]}, "rightFrontLeg": {"rotation": ["math.sin(100*q.anim_time)*4", 0, 0]}, "rightBackLeg": {"rotation": ["math.sin(100*q.anim_time)*4", 0, 0]}, "leftBackLeg": {"rotation": ["math.sin(100*q.anim_time)*4", 0, 0]}}}, "animation.ithiefi_ditsh_spider_blocksian.walk": {"loop": true, "animation_length": 1.7918, "anim_time_update": "q.anim_time + (q.delta_time * (q.modified_move_speed * 2.4))", "bones": {"torso": {"rotation": ["math.sin(800*q.anim_time-80)*2", 0, "math.cos(400*q.anim_time-80)*2"], "position": [0, "math.sin(800*q.anim_time)*0.5", 0]}, "leftFrontLeg": {"rotation": ["-math.sin(400*q.anim_time)*12", "-math.cos(400*q.anim_time)*12", "math.sin(400*q.anim_time)*12"]}, "head": {"rotation": ["math.cos(800*q.anim_time-120)*4", 0, "math.sin(400*q.anim_time-120)*4"]}, "leftFrontLegJoint": {"rotation": ["-math.sin(400*q.anim_time-40)*24", "-math.cos(400*q.anim_time-40)*24", "math.sin(400*q.anim_time-40)*24"]}, "rightFrontLeg": {"rotation": ["-math.cos(400*q.anim_time)*12", "math.sin(400*q.anim_time)*12", "-math.cos(400*q.anim_time)*12"]}, "rightFrontLegJoint": {"rotation": ["-math.cos(400*q.anim_time-40)*24", "math.sin(400*q.anim_time-40)*24", "-math.cos(400*q.anim_time-40)*24"]}, "rightBackLeg": {"rotation": ["-math.sin(400*q.anim_time-180)*12", "-(math.cos(400*q.anim_time-180)*24)", "math.sin(400*q.anim_time-180)*12"]}, "rightBackLegJoint": {"rotation": ["-math.sin(400*q.anim_time-(40+180))*24", "-(math.cos(400*q.anim_time-(40+180))*24)", "math.sin(400*q.anim_time-(40+180))*24"]}, "leftBackLeg": {"rotation": ["-math.cos(400*q.anim_time-180)*12", "math.sin(400*q.anim_time-180)*24", "-math.cos(400*q.anim_time-180)*12"]}, "leftBackLegJoint": {"rotation": ["-math.cos(400*q.anim_time-(40+180))*24", "math.sin(400*q.anim_time-(40+180))*24", "-math.cos(400*q.anim_time-(40+180))*24"]}}, "sound_effects": {"0.0833": {"effect": "walk"}, "1.0417": {"effect": "walk"}}}, "animation.ithiefi_ditsh_spider_blocksian.run": {"loop": true, "animation_length": 1.7918, "anim_time_update": "q.anim_time + (q.delta_time * (q.modified_move_speed * 2.4))", "bones": {"torso": {"rotation": ["math.sin(800*q.anim_time-80)*2", 0, "math.cos(400*q.anim_time-80)*2"], "position": [0, "math.sin(800*q.anim_time)*0.5", 0]}, "leftFrontLeg": {"rotation": ["-math.sin(400*q.anim_time)*12", "-math.cos(400*q.anim_time)*12", "math.sin(400*q.anim_time)*12"]}, "head": {"rotation": ["math.cos(800*q.anim_time-120)*4", 0, "math.sin(400*q.anim_time-120)*4"]}, "leftFrontLegJoint": {"rotation": ["-math.sin(400*q.anim_time-40)*24", "-math.cos(400*q.anim_time-40)*24", "math.sin(400*q.anim_time-40)*24"]}, "rightFrontLeg": {"rotation": ["-math.cos(400*q.anim_time)*12", "math.sin(400*q.anim_time)*12", "-math.cos(400*q.anim_time)*12"]}, "rightFrontLegJoint": {"rotation": ["-math.cos(400*q.anim_time-40)*24", "math.sin(400*q.anim_time-40)*24", "-math.cos(400*q.anim_time-40)*24"]}, "rightBackLeg": {"rotation": ["-math.sin(400*q.anim_time-180)*12", "-(math.cos(400*q.anim_time-180)*24)", "math.sin(400*q.anim_time-180)*12"]}, "rightBackLegJoint": {"rotation": ["-math.sin(400*q.anim_time-(40+180))*24", "-(math.cos(400*q.anim_time-(40+180))*24)", "math.sin(400*q.anim_time-(40+180))*24"]}, "leftBackLeg": {"rotation": ["-math.cos(400*q.anim_time-180)*12", "math.sin(400*q.anim_time-180)*24", "-math.cos(400*q.anim_time-180)*12"]}, "leftBackLegJoint": {"rotation": ["-math.cos(400*q.anim_time-(40+180))*24", "math.sin(400*q.anim_time-(40+180))*24", "-math.cos(400*q.anim_time-(40+180))*24"]}}, "sound_effects": {"0.0": {"effect": "run"}}}, "animation.ithiefi_ditsh_spider_blocksian.attack": {"loop": "hold_on_last_frame", "animation_length": 0.5, "bones": {"torso": {"rotation": {"0.0": [0, 0, 0], "0.0833": [7.5, 0, 0], "0.1667": [7.5, 0, 0], "0.25": [30, 0, 0], "0.4167": [0, 0, 0]}}, "leftFrontLeg": {"rotation": {"0.0": [0, 0, 0], "0.2083": [-67.5, 0, 0], "0.3333": [0, 0, 0]}}, "root": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-15, 0, 0], "0.2917": [0, 0, 0]}}, "head": {"rotation": {"0.0833": [0, 0, 0], "0.1667": [7.5, 0, 0], "0.25": [7.5, 0, 0], "0.3333": [30, 0, 0], "0.5": [0, 0, 0]}}, "leftFrontLegJoint": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-58.95658, 24.1782, -18.32252], "0.2917": [-58.95658, 24.1782, -18.32252], "0.4583": [0, 0, 0]}}, "rightFrontLeg": {"rotation": {"0.0": [0, 0, 0], "0.2083": [-67.5, 0, 0], "0.3333": [0, 0, 0]}}, "rightFrontLegJoint": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-58.95658, -24.1782, 18.32252], "0.2917": [-58.95658, -24.1782, 18.32252], "0.4583": [0, 0, 0]}}, "rightBackLeg": {"rotation": {"0.0": [0, 0, 0], "0.125": [11, 0, 0], "0.2083": [10, 0, 0], "0.2917": [0, 0, 0]}}, "rightBackLegJoint": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-7.31594, -2.15393, -2.26758], "0.2917": [0, 0, 0]}}, "leftBackLeg": {"rotation": {"0.0": [0, 0, 0], "0.125": [11, 0, 0], "0.2083": [10, 0, 0], "0.2917": [0, 0, 0]}}, "leftBackLegJoint": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-7.31594, -2.15393, -2.26758], "0.2917": [0, 0, 0]}}}}}}