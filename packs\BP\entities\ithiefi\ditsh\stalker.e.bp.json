{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:stalker", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:playing_music": {"type": "bool", "client_sync": false, "default": false}, "ditsh:teleport_weight": {"type": "int", "client_sync": false, "default": 15, "range": [1, 100]}}}, "component_groups": {"ditsh:music_playing": {"minecraft:timer": {"looping": true, "time": 53, "time_down_event": {"event": "ditsh:maintain_chase_music"}}}}, "events": {"minecraft:entity_spawned": {}, "ditsh:on_death": {}, "ditsh:start_chase_music": {"add": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": true}}, "ditsh:stop_chase_music": {"remove": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": false}}, "ditsh:maintain_chase_music": {}, "ditsh:on_kill": {"queue_command": {"command": "playsound mob.ditsh.stalker.kill @a ~ ~ ~"}}, "ditsh:teleport_to_player": {}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(1,3)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "stalker", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.6, "height": 1.9}, "minecraft:health": {"value": 40, "max": 40}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement": {"value": 0.25}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.hurt_by_target": {"priority": 1}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "within_radius": 25.0, "reselect_targets": true, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_game_mode", "subject": "other", "operator": "!=", "value": "creative"}, {"test": "is_game_mode", "subject": "other", "operator": "!=", "value": "spectator"}]}, "max_dist": 35}]}, "minecraft:behavior.melee_attack": {"priority": 3, "speed_multiplier": 1.25, "track_target": true}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 1}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 9}, "minecraft:attack": {"damage": 6, "effect_name": "nausea", "effect_duration": 3}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:environment_sensor": {"triggers": [{"filters": {"test": "has_target", "subject": "self", "value": true}, "event": "ditsh:teleport_to_player"}]}}}}