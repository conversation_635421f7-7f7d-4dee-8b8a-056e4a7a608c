import { GameMode } from "@minecraft/server";
import { getDistance, getRandomLocation } from "../utilities/vector3";
const TELEPORT_OFFSET_DISTANCE = 4;
const DEFAULT_TELEPORT_WEIGHT = 5;
export function stalkerTele<PERSON><PERSON>andler(stalker) {
    try {
        const teleportWeight = stalker.getProperty("ditsh:teleport_weight") || DEFAULT_TELEPORT_WEIGHT;
        const randomRoll = Math.floor(Math.random() * 100) + 1;
        if (randomRoll > teleportWeight) {
            return;
        }
        const location = stalker.location;
        const players = stalker.dimension.getPlayers({
            location: location,
            maxDistance: 256,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        if (players.length === 0) {
            return;
        }
        let nearestPlayer = players[0];
        let nearestDistance = getDistance(location, nearestPlayer.location);
        for (const player of players) {
            const distance = getDistance(location, player.location);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestPlayer = player;
            }
        }
        let finalTeleportLocation;
        const playerViewDirection = nearestPlayer.getViewDirection();
        const behindPlayerLocation = {
            x: nearestPlayer.location.x - (playerViewDirection.x * TELEPORT_OFFSET_DISTANCE),
            y: nearestPlayer.location.y,
            z: nearestPlayer.location.z - (playerViewDirection.z * TELEPORT_OFFSET_DISTANCE)
        };
        const behindPlayerBlock = stalker.dimension.getBlock(behindPlayerLocation);
        if (behindPlayerBlock?.isAir) {
            finalTeleportLocation = behindPlayerLocation;
        }
        if (!finalTeleportLocation) {
            const sidePlayerLocation = {
                x: nearestPlayer.location.x + (playerViewDirection.z * TELEPORT_OFFSET_DISTANCE),
                y: nearestPlayer.location.y,
                z: nearestPlayer.location.z - (playerViewDirection.x * TELEPORT_OFFSET_DISTANCE)
            };
            const sidePlayerBlock = stalker.dimension.getBlock(sidePlayerLocation);
            if (sidePlayerBlock?.isAir) {
                finalTeleportLocation = sidePlayerLocation;
            }
        }
        if (!finalTeleportLocation) {
            finalTeleportLocation = getRandomLocation(nearestPlayer.location, stalker.dimension, 2, 4, 0, true);
        }
        if (!finalTeleportLocation) {
            finalTeleportLocation = getRandomLocation(nearestPlayer.location, stalker.dimension, 1, 2, 0, true);
        }
        if (!finalTeleportLocation) {
            finalTeleportLocation = {
                x: nearestPlayer.location.x,
                y: nearestPlayer.location.y,
                z: nearestPlayer.location.z
            };
        }
        stalker.teleport(finalTeleportLocation);
        stalker.dimension.playSound("mob.ditsh.stalker.teleport", finalTeleportLocation);
    }
    catch (error) {
        console.warn(`Failed to teleport Stalker: ${error}`);
    }
}
